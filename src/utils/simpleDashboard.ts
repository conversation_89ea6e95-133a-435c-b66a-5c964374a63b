import chalk from 'chalk';
import { DashboardData } from '../types';

/**
 * Simple console-based dashboard for users who prefer traditional console output
 * This is a lightweight alternative to the split-screen dashboard
 */
export class SimpleDashboard {
  private isRunning: boolean = false;
  private refreshTimer?: NodeJS.Timeout;
  private dashboardData: DashboardData;
  private lastUpdate: number = 0;
  private updateInterval: number = 5000; // Update every 5 seconds

  constructor() {
    this.dashboardData = {
      botStatus: 'Starting...',
      networkInfo: {
        chainId: 0,
        blockNumber: 0,
        gasPrice: '0'
      },
      mempoolStats: {
        pendingTransactions: 0,
        relevantTransactions: 0,
        processedTransactions: 0
      },
      opportunityStats: {
        totalOpportunities: 0,
        successfulTrades: 0,
        failedTrades: 0,
        totalProfit: '0'
      },
      performanceMetrics: {
        avgResponseTime: 0,
        successRate: 0,
        uptime: 0
      },
      recentTransactions: [],
      gasStrategy: {
        currentStrategy: 'medium',
        baseFee: '0',
        priorityFee: '0',
        maxFee: '0'
      },
      configInfo: {
        enabledStrategies: [],
        tokenPairs: [],
        dexes: [],
        minProfitThreshold: '0'
      }
    };
  }

  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    console.log(chalk.blue.bold('🚀 MEV Bot - Simple Dashboard Mode'));
    console.log(chalk.gray('═'.repeat(60)));
    console.log(chalk.yellow('💡 Tip: Use Ctrl+C to stop the bot'));
    console.log(chalk.yellow('💡 Logs will appear below the status updates'));
    console.log(chalk.gray('═'.repeat(60)));

    // Initial status display
    this.displayStatus();

    // Set up periodic status updates
    this.refreshTimer = setInterval(() => {
      this.displayStatus();
    }, this.updateInterval);

    // Handle graceful shutdown
    process.on('SIGINT', () => {
      this.stop();
      process.exit(0);
    });

    process.on('SIGTERM', () => {
      this.stop();
      process.exit(0);
    });
  }

  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = undefined;
    }

    console.log(chalk.yellow('\n📊 Final Status Summary:'));
    this.displayStatus();
    console.log(chalk.blue.bold('\n✅ MEV Bot stopped gracefully'));
  }

  public updateData(data: Partial<DashboardData>): void {
    this.dashboardData = { ...this.dashboardData, ...data };
  }

  private displayStatus(): void {
    const now = Date.now();
    if (now - this.lastUpdate < this.updateInterval - 1000) {
      return; // Don't update too frequently
    }
    this.lastUpdate = now;

    // Clear previous status (but keep logs)
    console.log('\n' + chalk.gray('─'.repeat(60)));
    console.log(chalk.blue.bold(`📊 Status Update - ${new Date().toLocaleTimeString()}`));
    console.log(chalk.gray('─'.repeat(60)));

    // Bot Status
    const statusColor = this.getStatusColor(this.dashboardData.botStatus);
    console.log(`${chalk.cyan('🤖 Bot Status:')} ${statusColor(this.dashboardData.botStatus)}`);

    // Network Info
    console.log(`${chalk.cyan('🌐 Network:')} Chain ${this.dashboardData.networkInfo.chainId} | Block ${this.dashboardData.networkInfo.blockNumber} | Gas ${this.dashboardData.networkInfo.gasPrice} gwei`);

    // Mempool Stats
    const { mempoolStats } = this.dashboardData;
    console.log(`${chalk.cyan('📊 Mempool:')} ${mempoolStats.pendingTransactions} pending | ${chalk.green(mempoolStats.relevantTransactions)} relevant | ${mempoolStats.processedTransactions} processed`);

    // Opportunity Stats
    const { opportunityStats } = this.dashboardData;
    const profitColor = parseFloat(opportunityStats.totalProfit) > 0 ? chalk.green : chalk.gray;
    console.log(`${chalk.cyan('💰 Opportunities:')} ${opportunityStats.totalOpportunities} found | ${chalk.green(opportunityStats.successfulTrades)} success | ${chalk.red(opportunityStats.failedTrades)} failed | ${profitColor(opportunityStats.totalProfit)} ETH profit`);

    // Performance
    const { performanceMetrics } = this.dashboardData;
    const uptimeHours = Math.floor(performanceMetrics.uptime / 3600);
    const uptimeMinutes = Math.floor((performanceMetrics.uptime % 3600) / 60);
    console.log(`${chalk.cyan('⚡ Performance:')} ${performanceMetrics.avgResponseTime}ms avg | ${performanceMetrics.successRate}% success | ${uptimeHours}h ${uptimeMinutes}m uptime`);

    // Recent Activity
    if (this.dashboardData.recentTransactions.length > 0) {
      const recentTx = this.dashboardData.recentTransactions[0];
      console.log(`${chalk.cyan('🔄 Latest:')} ${recentTx.type} | ${recentTx.profit} ETH | ${recentTx.status}`);
    }

    // Configuration Summary
    const { configInfo } = this.dashboardData;
    if (configInfo.enabledStrategies.length > 0) {
      console.log(`${chalk.cyan('⚙️  Config:')} ${configInfo.enabledStrategies.join(', ')} | ${configInfo.tokenPairs.length} pairs | Min profit: ${configInfo.minProfitThreshold} ETH`);
    }

    console.log(chalk.gray('─'.repeat(60)));
  }

  private getStatusColor(status: string): (text: string) => string {
    switch (status.toLowerCase()) {
      case 'running':
      case 'active':
      case 'monitoring':
        return chalk.green;
      case 'starting':
      case 'initializing':
        return chalk.yellow;
      case 'error':
      case 'failed':
      case 'stopped':
        return chalk.red;
      case 'paused':
      case 'waiting':
        return chalk.blue;
      default:
        return chalk.white;
    }
  }

  public logMessage(level: string, message: string, data?: any): void {
    if (!this.isRunning) {
      return;
    }

    const timestamp = new Date().toLocaleTimeString();
    const levelColor = this.getLogLevelColor(level);
    const levelText = level.toUpperCase().padEnd(5);

    let logLine = `${chalk.gray(timestamp)} ${levelColor(levelText)} ${message}`;

    if (data) {
      try {
        const dataStr = JSON.stringify(data);
        if (dataStr.length < 100) {
          logLine += ` ${chalk.gray(dataStr)}`;
        } else {
          logLine += ` ${chalk.gray('[data]')}`;
        }
      } catch {
        logLine += ` ${chalk.gray('[object]')}`;
      }
    }

    console.log(logLine);
  }

  private getLogLevelColor(level: string): (text: string) => string {
    switch (level.toLowerCase()) {
      case 'error':
        return chalk.red;
      case 'warn':
      case 'warning':
        return chalk.yellow;
      case 'info':
        return chalk.blue;
      case 'debug':
        return chalk.gray;
      case 'success':
        return chalk.green;
      default:
        return chalk.white;
    }
  }
}

// Export singleton instance
export const simpleDashboard = new SimpleDashboard();
