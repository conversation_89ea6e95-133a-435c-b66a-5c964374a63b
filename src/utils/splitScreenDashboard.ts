import blessed from 'blessed';
import chalk from 'chalk';
import { ethers } from 'ethers';
import { LogEntry, LogLevel } from '../types';
import { config } from '../config';

export interface SplitScreenConfig {
  title: string;
  refreshRate: number; // milliseconds
  maxLogLines: number;
  maxStatusLines: number;
  forceLogRerender?: boolean; // Force immediate rerender on log updates (default: true)
  logRenderThrottle?: number; // Minimum ms between log renders (default: 50ms)
}

export interface DashboardData {
  // Network status
  currentBlock: number;
  networkName: string;
  
  // Bot status
  isRunning: boolean;
  uptime: number;
  lastActivity: number;
  
  // Strategy status
  flashloanEnabled: boolean;
  mevShareEnabled: boolean;
  arbitrageEnabled: boolean;
  
  // Statistics
  totalTransactions: number;
  relevantTransactions: number;
  opportunitiesFound: number;
  opportunitiesExecuted: number;
  totalProfit: bigint;
  avgGasPrice: bigint;
  
  // Configuration
  configuration: {
    tokenPairs: string[];
    dexes: string[];
    minProfitThreshold: string;
    maxGasPrice: string;
  };
  
  // Successful transactions
  successfulTransactions: Array<{
    timestamp: number;
    type: string;
    profit: bigint;
    gasUsed: bigint;
    txHash?: string;
    confidence?: number;
    details?: string;
  }>;
  
  // Error tracking
  errors: number;
  lastError?: string;
}

export class SplitScreenDashboard {
  private screen!: blessed.Widgets.Screen;
  private statusBox!: blessed.Widgets.BoxElement;
  private logBox!: blessed.Widgets.Log;
  private titleBox!: blessed.Widgets.BoxElement;
  private isRunning: boolean = false;
  private refreshTimer?: NodeJS.Timeout;
  private config: SplitScreenConfig;
  private dashboardData: DashboardData;
  private logEntries: LogEntry[] = [];
  private mouseInteractionMode: boolean = true;
  private lastLogRender: number = 0;
  private logRenderThrottle: number = 50; // Minimum ms between log renders
  private forceLogRerender: boolean = true; // Force immediate rerender on log updates

  constructor(config: Partial<SplitScreenConfig> = {}) {
    this.config = {
      title: 'MEV Bot Dashboard',
      refreshRate: 1000,
      maxLogLines: 1000,
      maxStatusLines: 50,
      forceLogRerender: true,
      logRenderThrottle: 50,
      ...config
    };

    // Set instance properties from config
    this.forceLogRerender = this.config.forceLogRerender ?? true;
    this.logRenderThrottle = this.config.logRenderThrottle ?? 50;

    this.dashboardData = this.getDefaultDashboardData();
    this.initializeScreen();
  }

  private getDefaultDashboardData(): DashboardData {
    return {
      currentBlock: 0,
      networkName: 'Unknown',
      isRunning: false,
      uptime: 0,
      lastActivity: 0,
      flashloanEnabled: false,
      mevShareEnabled: false,
      arbitrageEnabled: false,
      totalTransactions: 0,
      relevantTransactions: 0,
      opportunitiesFound: 0,
      opportunitiesExecuted: 0,
      totalProfit: BigInt(0),
      avgGasPrice: BigInt(0),
      configuration: {
        tokenPairs: [],
        dexes: [],
        minProfitThreshold: '0',
        maxGasPrice: '0'
      },
      successfulTransactions: [],
      errors: 0,
      lastError: undefined
    };
  }

  private initializeScreen(): void {
    // Create the main screen
    this.screen = blessed.screen({
      smartCSR: true,
      title: this.config.title,
      dockBorders: true,
      fullUnicode: true,
      autoPadding: true,
      mouse: true,
      sendFocus: true,
      grabKeys: false,
      input: process.stdin,
      output: process.stdout
    });

    // Title bar
    this.titleBox = blessed.box({
      top: 0,
      left: 0,
      width: '100%',
      height: 3,
      content: '',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        bg: 'blue',
        border: {
          fg: 'cyan'
        }
      }
    });

    // Status dashboard (left side)
    this.statusBox = blessed.box({
      label: ' Status Dashboard ',
      top: 3,
      left: 0,
      width: '50%',
      height: '100%-3',
      content: '',
      tags: true,
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        border: {
          fg: 'cyan'
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'cyan'
        },
        style: {
          inverse: true
        }
      }
    });

    // Log panel (right side)
    this.logBox = blessed.log({
      label: ' Live Logs ',
      top: 3,
      left: '50%',
      width: '50%',
      height: '100%-3',
      border: {
        type: 'line'
      },
      style: {
        fg: 'white',
        border: {
          fg: 'green'
        }
      },
      scrollable: true,
      alwaysScroll: true,
      mouse: true,
      keys: true,
      vi: true,
      scrollbar: {
        ch: ' ',
        track: {
          bg: 'green'
        },
        style: {
          inverse: true
        }
      },
      tags: true,
      clickable: false,
      input: true
    });

    // Add all elements to screen
    this.screen.append(this.titleBox);
    this.screen.append(this.statusBox);
    this.screen.append(this.logBox);

    // Key bindings
    this.screen.key(['escape', 'q', 'C-c'], () => {
      this.stop();
      process.exit(0);
    });

    this.screen.key(['r'], () => {
      this.refresh();
    });

    // Toggle mouse mode for copy/paste
    this.screen.key(['m', 'C-m'], () => {
      this.toggleMouseMode();
    });

    // Tab to switch focus between panels
    this.screen.key(['tab'], () => {
      if (this.screen.focused === this.logBox) {
        this.statusBox.focus();
      } else {
        this.logBox.focus();
      }
      this.updateFocusStyles();
      this.screen.render();
    });

    // Arrow keys for scrolling when focused
    this.screen.key(['up'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(-1);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(-1);
        this.screen.render();
      }
    });

    this.screen.key(['down'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(1);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(1);
        this.screen.render();
      }
    });

    this.screen.key(['pageup'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(-10);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(-10);
        this.screen.render();
      }
    });

    this.screen.key(['pagedown'], () => {
      if (this.screen.focused === this.statusBox) {
        this.statusBox.scroll(10);
        this.screen.render();
      } else if (this.screen.focused === this.logBox) {
        this.logBox.scroll(10);
        this.screen.render();
      }
    });

    // Mouse click to focus panels (only in mouse interaction mode)
    this.statusBox.on('click', () => {
      if (this.mouseInteractionMode) {
        this.statusBox.focus();
        this.updateFocusStyles();
        this.screen.render();
      }
    });

    this.logBox.on('click', () => {
      if (this.mouseInteractionMode) {
        this.logBox.focus();
        this.updateFocusStyles();
        this.screen.render();
      }
    });

    // Focus events for visual feedback
    this.statusBox.on('focus', () => {
      this.updateFocusStyles();
    });

    this.logBox.on('focus', () => {
      this.updateFocusStyles();
    });

    // Mouse wheel scrolling (only in mouse interaction mode)
    this.statusBox.on('wheelup', () => {
      if (this.mouseInteractionMode) {
        this.statusBox.scroll(-3);
        this.screen.render();
      }
    });

    this.statusBox.on('wheeldown', () => {
      if (this.mouseInteractionMode) {
        this.statusBox.scroll(3);
        this.screen.render();
      }
    });

    this.logBox.on('wheelup', () => {
      if (this.mouseInteractionMode) {
        this.logBox.scroll(-3);
        this.screen.render();
      }
    });

    this.logBox.on('wheeldown', () => {
      if (this.mouseInteractionMode) {
        this.logBox.scroll(3);
        this.screen.render();
      }
    });

    // Focus on log box by default for scrolling
    this.logBox.focus();
  }

  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Update logger to suppress console output
    const { logger } = require('./logger');
    logger.updateForSplitScreen(true);

    // Initialize in mouse mode
    this.mouseInteractionMode = true;
    this.enableMouseInteraction();

    this.screen.render();

    // Start refresh timer
    this.refreshTimer = setInterval(() => {
      this.refresh();
    }, this.config.refreshRate);

    // Initial render
    this.refresh();
  }

  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    // Restore terminal to normal mode
    this.disableMouseInteraction();

    // Restore logger console output
    const { logger } = require('./logger');
    logger.updateForSplitScreen(false);

    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = undefined;
    }

    if (this.screen) {
      this.screen.destroy();
    }
  }

  public updateDashboardData(data: Partial<DashboardData>): void {
    this.dashboardData = { ...this.dashboardData, ...data };
    if (this.isRunning) {
      this.refresh();
    }
  }

  public addLogEntry(entry: LogEntry): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(entry.level)) {
      return;
    }

    this.logEntries.push(entry);

    // Keep only recent entries
    if (this.logEntries.length > this.config.maxLogLines) {
      this.logEntries = this.logEntries.slice(-this.config.maxLogLines);
    }

    if (this.isRunning) {
      this.updateLogDisplay();

      // Force immediate rerender if enabled and throttle allows
      if (this.forceLogRerender) {
        this.forceLogPanelRerender();
      }
    }
  }

  public addLog(level: LogLevel, message: string, data?: any): void {
    // Filter logs based on configured log level
    if (!this.shouldShowLogLevel(level)) {
      return;
    }

    const entry: LogEntry = {
      level,
      message,
      timestamp: Date.now(),
      data
    };
    this.addLogEntry(entry);
  }

  /**
   * Force immediate rerender of the log panel with throttling
   */
  private forceLogPanelRerender(): void {
    const now = Date.now();

    // Throttle renders to prevent excessive screen updates
    if (now - this.lastLogRender < this.logRenderThrottle) {
      return;
    }

    this.lastLogRender = now;

    try {
      // Force immediate render of the screen to update log panel
      if (this.screen && this.isRunning) {
        this.screen.render();
      }
    } catch (error: any) {
      // Silently handle render errors to prevent spam
      if (error?.message && !error.message.includes('destroyed')) {
        // Only log if it's not a destroyed screen error
      }
    }
  }

  private refresh(): void {
    if (!this.isRunning) {
      return;
    }

    this.updateTitleDisplay();
    this.updateStatusDisplay();
    this.updateLogDisplay();
    this.screen.render();
  }

  private updateTitleDisplay(): void {
    const modeText = this.mouseInteractionMode ?
      'Mouse Mode: Scrolling & Navigation' :
      'Text Mode: Copy/Paste Enabled';
    this.updateTitleWithMode(modeText);
  }

  private updateStatusDisplay(): void {
    const data = this.dashboardData;
    const now = Date.now();
    const uptime = this.formatUptime(data.uptime);
    const lastActivity = this.formatTimeSince(data.lastActivity);
    
    let content = '';
    
    // Status Overview
    content += '{bold}{yellow-fg}📊 STATUS OVERVIEW{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Status: ${data.isRunning ? '{green-fg}RUNNING{/}' : '{red-fg}STOPPED{/}'}\n`;
    content += `Network: {cyan-fg}${data.networkName}{/} | Block: {yellow-fg}${data.currentBlock}{/}\n`;
    content += `Uptime: {green-fg}${uptime}{/}\n`;
    content += `Last Activity: {gray-fg}${lastActivity}{/}\n\n`;

    // Strategy Status
    content += '{bold}{blue-fg}⚡ STRATEGY STATUS{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Flashloan: ${data.flashloanEnabled ? '{green-fg}ENABLED{/}' : '{red-fg}DISABLED{/}'}\n`;
    content += `MEV-Share: ${data.mevShareEnabled ? '{green-fg}ENABLED{/}' : '{red-fg}DISABLED{/}'}\n`;
    content += `Arbitrage: ${data.arbitrageEnabled ? '{green-fg}ENABLED{/}' : '{red-fg}DISABLED{/}'}\n\n`;

    // Statistics
    content += '{bold}{magenta-fg}📈 STATISTICS{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Total Transactions: {yellow-fg}${data.totalTransactions}{/}\n`;
    content += `Relevant: {green-fg}${data.relevantTransactions}{/}\n`;
    content += `Opportunities Found: {cyan-fg}${data.opportunitiesFound}{/}\n`;
    content += `Opportunities Executed: {green-fg}${data.opportunitiesExecuted}{/}\n`;
    content += `Total Profit: {green-fg}${ethers.formatEther(data.totalProfit)} ETH{/}\n`;
    content += `Avg Gas Price: {yellow-fg}${ethers.formatUnits(data.avgGasPrice, 'gwei')} gwei{/}\n`;
    content += `Errors: ${data.errors > 0 ? `{red-fg}${data.errors}{/}` : '{green-fg}0{/}'}\n\n`;

    // Configuration
    content += '{bold}{cyan-fg}⚙️  CONFIGURATION{/}\n';
    content += '─'.repeat(40) + '\n';
    content += `Token Pairs: {yellow-fg}${data.configuration.tokenPairs.join(', ') || 'None'}{/}\n`;
    content += `DEXes: {cyan-fg}${data.configuration.dexes.join(', ') || 'None'}{/}\n`;
    content += `Min Profit: {green-fg}${data.configuration.minProfitThreshold} ETH{/}\n`;
    content += `Max Gas: {yellow-fg}${data.configuration.maxGasPrice} gwei{/}\n\n`;

    // Recent Successful Transactions
    if (data.successfulTransactions.length > 0) {
      content += '{bold}{green-fg}💰 RECENT SUCCESSFUL TRANSACTIONS{/}\n';
      content += '─'.repeat(40) + '\n';
      
      const recent = data.successfulTransactions.slice(-5).reverse();
      for (const tx of recent) {
        const time = new Date(tx.timestamp).toLocaleTimeString();
        const profit = ethers.formatEther(tx.profit);
        const gasUsed = ethers.formatEther(tx.gasUsed);
        
        content += `{gray-fg}${time}{/} {green-fg}${tx.type.toUpperCase()}{/} `;
        content += `Profit: {green-fg}${profit} ETH{/} `;
        content += `Gas: {yellow-fg}${gasUsed} ETH{/}`;
        if (tx.confidence) {
          content += ` Conf: {cyan-fg}${tx.confidence}%{/}`;
        }
        if (tx.details) {
          content += ` {gray-fg}(${tx.details}){/}`;
        }
        content += '\n';
      }
    }

    this.statusBox.setContent(content);
  }

  private updateLogDisplay(): void {
    // Add recent log entries to the log box
    const recentLogs = this.logEntries.slice(-20); // Show last 20 logs
    
    for (const entry of recentLogs) {
      const timestamp = new Date(entry.timestamp).toLocaleTimeString();
      const levelColor = this.getLogLevelColor(entry.level);
      const levelText = entry.level.toUpperCase().padEnd(5);
      
      let logLine = `{gray-fg}${timestamp}{/} {${levelColor}}${levelText}{/} ${entry.message}`;
      
      if (entry.data) {
        logLine += ` {gray-fg}${JSON.stringify(entry.data)}{/}`;
      }
      
      this.logBox.log(logLine);
    }
    
    // Clear processed entries to avoid duplicates
    this.logEntries = [];
  }

  private getLogLevelColor(level: LogLevel): string {
    switch (level) {
      case LogLevel.ERROR:
        return 'red-fg';
      case LogLevel.WARN:
        return 'yellow-fg';
      case LogLevel.INFO:
        return 'cyan-fg';
      case LogLevel.DEBUG:
        return 'gray-fg';
      default:
        return 'white-fg';
    }
  }

  /**
   * Check if a log level should be shown based on configured log level
   */
  private shouldShowLogLevel(level: LogLevel): boolean {
    const configuredLevel = config.logLevel.toLowerCase();
    const levelHierarchy = {
      'error': 0,
      'warn': 1,
      'info': 2,
      'debug': 3
    };

    const currentLevelValue = levelHierarchy[level.toLowerCase() as keyof typeof levelHierarchy];
    const configuredLevelValue = levelHierarchy[configuredLevel as keyof typeof levelHierarchy];

    // If level is not recognized, default to showing it
    if (currentLevelValue === undefined || configuredLevelValue === undefined) {
      return true;
    }

    // Show log if its level is at or above the configured level
    return currentLevelValue <= configuredLevelValue;
  }

  private formatUptime(uptime: number): string {
    if (uptime === 0) return '0s';
    
    const seconds = Math.floor(uptime / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`;
    } else {
      return `${seconds}s`;
    }
  }

  private formatTimeSince(timestamp: number): string {
    if (timestamp === 0) return 'Never';

    const diff = Date.now() - timestamp;
    const seconds = Math.floor(diff / 1000);

    if (seconds < 60) {
      return `${seconds}s ago`;
    } else if (seconds < 3600) {
      return `${Math.floor(seconds / 60)}m ago`;
    } else {
      return `${Math.floor(seconds / 3600)}h ago`;
    }
  }

  private updateFocusStyles(): void {
    // Update border colors based on focus
    if (this.screen.focused === this.statusBox) {
      this.statusBox.style.border.fg = 'yellow';
      this.logBox.style.border.fg = 'green';
    } else if (this.screen.focused === this.logBox) {
      this.statusBox.style.border.fg = 'cyan';
      this.logBox.style.border.fg = 'yellow';
    } else {
      this.statusBox.style.border.fg = 'cyan';
      this.logBox.style.border.fg = 'green';
    }
  }

  private toggleMouseMode(): void {
    this.mouseInteractionMode = !this.mouseInteractionMode;

    if (this.mouseInteractionMode) {
      // Enable mouse interaction mode (scrolling, clicking)
      this.enableMouseInteraction();
      this.updateTitleWithMode('Mouse Mode: Scrolling & Navigation');
      this.hideTextModeInstructions();
    } else {
      // Disable mouse interaction for text selection
      this.disableMouseInteraction();
      this.updateTitleWithMode('Text Mode: Mouse selection enabled - Press m to return');
      this.showTextModeInstructions();
    }

    this.screen.render();
  }

  private showTextModeInstructions(): void {
    // Add instructions to the log panel
    this.logBox.log('{yellow-fg}═══════════════════════════════════════{/}');
    this.logBox.log('{yellow-fg}📋 TEXT SELECTION MODE ENABLED{/}');
    this.logBox.log('{yellow-fg}═══════════════════════════════════════{/}');
    this.logBox.log('{green-fg}✅ You can now select text with your mouse{/}');
    this.logBox.log('{green-fg}✅ Click and drag to select log text{/}');
    this.logBox.log('{green-fg}✅ Use Ctrl+C to copy selected text{/}');
    this.logBox.log('{green-fg}✅ Press "m" to return to mouse mode{/}');
    this.logBox.log('{yellow-fg}═══════════════════════════════════════{/}');
    this.logBox.log('');
  }

  private hideTextModeInstructions(): void {
    // Instructions will naturally scroll away as new logs come in
    // No need to actively remove them
  }

  private enableMouseInteraction(): void {
    // Enable mouse tracking for blessed interface
    process.stdout.write('\x1b[?1000h'); // Enable mouse tracking
    process.stdout.write('\x1b[?1002h'); // Enable button event tracking
    process.stdout.write('\x1b[?1003h'); // Enable any event tracking
  }

  private disableMouseInteraction(): void {
    // Disable mouse tracking to allow text selection
    process.stdout.write('\x1b[?1000l'); // Disable mouse tracking
    process.stdout.write('\x1b[?1002l'); // Disable button event tracking
    process.stdout.write('\x1b[?1003l'); // Disable any event tracking
  }

  private updateTitleWithMode(modeText: string): void {
    const now = new Date();
    const uptime = this.formatUptime(this.dashboardData.uptime);
    const status = this.dashboardData.isRunning ? '{green-fg}RUNNING{/}' : '{red-fg}STOPPED{/}';

    const title = `{center}{bold}🤖 MEV Bot Dashboard - ${status} | ${modeText} | Press 'm' to toggle | ${now.toLocaleTimeString()}{/}`;
    this.titleBox.setContent(title);
  }

  public getScreen(): blessed.Widgets.Screen {
    return this.screen;
  }

  public isActive(): boolean {
    return this.isRunning;
  }

  /**
   * Enable or disable force rerendering of logs
   */
  public setForceLogRerender(enabled: boolean): void {
    this.forceLogRerender = enabled;
  }

  /**
   * Set the throttle time for log renders
   */
  public setLogRenderThrottle(throttleMs: number): void {
    this.logRenderThrottle = Math.max(10, throttleMs); // Minimum 10ms
  }

  /**
   * Get current force rerender status
   */
  public getForceLogRerender(): boolean {
    return this.forceLogRerender;
  }
}

export const splitScreenDashboard = new SplitScreenDashboard();
