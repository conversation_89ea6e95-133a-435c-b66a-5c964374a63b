import { ethers } from 'ethers';
import { MEVBot } from './core/bot';
import { SimpleMEVBot, runSimpleBot } from './simple-bot';
import { logger } from './utils/logger';
import { enhancedLogger } from './utils/enhancedLogger';
import { config } from './config';

async function main() {
  // Initialize dashboard based on mode
  const dashboardMode = process.env.DASHBOARD_MODE;
  const splitScreenEnabled = process.env.SPLIT_SCREEN_DASHBOARD === 'true';

  console.log(`Dashboard mode: ${dashboardMode}, Split screen: ${splitScreenEnabled}`);

  if (dashboardMode === 'simple') {
    const { simpleDashboard } = await import('./utils/simpleDashboard');

    // Start simple dashboard
    simpleDashboard.start();

    // Update logger to use simple dashboard
    logger.updateForSimpleDashboard(simpleDashboard);
  } else if (dashboardMode === 'console') {
    // Console mode - just use regular console logging
    logger.info('🖥️  Using console mode (use npm run dev for split-screen dashboard)');
  } else if (splitScreenEnabled || (!dashboardMode && !splitScreenEnabled)) {
    // Default to split screen if no mode specified or explicitly enabled
    const { splitScreenDashboard } = await import('./utils/splitScreenDashboard');

    // Start split screen dashboard immediately to capture all logs
    splitScreenDashboard.start();

    // Give it a moment to initialize
    await new Promise(resolve => setTimeout(resolve, 100));
  } else {
    // Fallback to console mode
    logger.info('🖥️  Using console mode');
  }

  logger.info('🤖 Initializing Advanced MEV Bot...');

  try {
    // Use the full MEV bot implementation
    const bot = new MEVBot();

    // Handle graceful shutdown with improved signal handling
    let isShuttingDown = false;

    const gracefulShutdown = async (signal: string) => {
      if (isShuttingDown) {
        logger.warn(`Already shutting down, ignoring ${signal}`);
        return;
      }

      isShuttingDown = true;
      logger.info(`Received ${signal}, shutting down gracefully...`);

      try {
        await Promise.race([
          bot.stop(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Shutdown timeout')), 10000)
          )
        ]);
        logger.info('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Shutdown error, forcing exit:', error);
        process.exit(1);
      }
    };

    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.logError(error, 'UncaughtException');
      if (!isShuttingDown) {
        bot.emergencyStop();
        process.exit(1);
      }
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled Rejection at:', { promise, reason });
      if (!isShuttingDown) {
        bot.emergencyStop();
        process.exit(1);
      }
    });

    // Start the bot
    await bot.start();

    // Status is now handled by the live dashboard - no need for periodic logs

    logger.info('🚀 Advanced MEV Bot is now running! Press Ctrl+C to stop.');

  } catch (error) {
    logger.logError(error as Error, 'main');

    // Fallback to simple bot if advanced bot fails
    logger.warn('⚠️  Advanced MEV Bot failed, falling back to Simple Bot...');
    try {
      await runSimpleBot();
    } catch (fallbackError) {
      logger.logError(fallbackError as Error, 'fallback');
      process.exit(1);
    }
  }
}

// Start the application
main().catch((error) => {
  logger.logError(error, 'main');
  process.exit(1);
});
